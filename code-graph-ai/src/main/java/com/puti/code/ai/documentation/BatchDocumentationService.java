package com.puti.code.ai.documentation;

import com.puti.code.ai.config.BatchProcessingConfig;
import com.puti.code.ai.support.TokenSupport;
import com.puti.code.base.model.MethodInfo;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;



/**
 * 分批文档生成服务
 * 处理大内容的分批生成和合并
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BatchDocumentationService {

    private final PromptBuilder promptBuilder;
    private final ChatClient simpleChatClient;
    private final ChatClient multiConversationChatClient;
    private final ExecutorService executorService;

    // 重试配置
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 10000;

    @Autowired
    public BatchDocumentationService(
            @Qualifier("simpleChatClient") ChatClient simpleChatClient,
            @Qualifier("multiConversationChatClient") ChatClient multiConversationChatClient,
            PromptBuilder promptBuilder) {
        this.promptBuilder = promptBuilder;

        this.simpleChatClient = simpleChatClient;
        this.multiConversationChatClient = multiConversationChatClient;

        this.executorService = Executors.newFixedThreadPool(BatchProcessingConfig.getMaxConcurrentBatches());
        log.info("分批文档生成服务已初始化，最大并发数: {}，支持多轮对话模式", BatchProcessingConfig.getMaxConcurrentBatches());
    }

    @PreDestroy
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            log.info("分批文档生成服务已关闭");
        }
    }

    /**
     * 检查是否需要分批处理
     *
     * @param context 文档生成上下文
     * @return 是否需要分批处理
     */
    public boolean needsBatchProcessing(DocumentationGenerationContext context) {
        // 获取当前层级的方法
        List<MethodInfo> methods = context.getMethodsAtLevel();

        // 获取模型配置
        int maxTokens = BatchProcessingConfig.getMaxTokensForLevel(context.getLevel());

        int estimatedTokens = TokenSupport.calculateTokens(methods, ModelConfig.getDefaultConfig().getEncodingType());

        boolean needsBatch = estimatedTokens > maxTokens;
        if (needsBatch) {
            log.info("第{}层内容过大({} tokens > {} tokens)，需要分批处理",
                    context.getLevel(), estimatedTokens, maxTokens);
        }

        return needsBatch;
    }

    /**
     * 分批生成文档（支持所有层级）
     * 根据配置选择使用传统并行模式或多轮对话模式
     *
     * @param context 文档生成上下文
     * @return 合并后的完整文档内容
     */
    public String generateBatchDocumentation(DocumentationGenerationContext context) {
        // 可以通过配置决定使用哪种模式，这里默认使用多轮对话模式
        return generateConversationalDocumentation(context);
//        boolean useConversationalMode = shouldUseConversationalMode(context);
//
//        if (useConversationalMode) {
//            return generateConversationalDocumentation(context);
//        } else {
//            return generateTraditionalBatchDocumentation(context);
//        }
    }


    /**
     * 多轮对话式文档生成
     * 手动管理对话上下文，每轮对话都携带初始prompt和历史对话信息
     *
     * @param context 文档生成上下文
     * @return 完整的文档内容
     */
    public String generateConversationalDocumentation(DocumentationGenerationContext context) {
        try {
            log.info("开始多轮对话式生成第{}层文档，入口点: {}", context.getLevel(), context.getEntryPointId());

            // 1. 构建分批提示词
            List<String> batchPrompts = promptBuilder.buildBatchPrompts(context);
            if (batchPrompts.isEmpty()) {
                log.error("构建分批提示词失败");
                return null;
            }

            log.info("将第{}层文档分为 {} 轮对话处理", context.getLevel(), batchPrompts.size());

            // 2. 创建对话上下文，设置初始prompt
            String initialPrompt = promptBuilder.buildInitializationPromptString(context, batchPrompts.size());
            ConversationContext conversationContext = ConversationContext.builder()
                    .initialPrompt(initialPrompt)
                    .docContext(context)
                    .rounds(new ArrayList<>())
                    .build();

            // 3. 逐轮进行对话，每轮都基于完整的上下文
            for (int i = 0; i < batchPrompts.size(); i++) {
                log.info("开始第 {} 轮对话 (共 {} 轮)", i + 1, batchPrompts.size());

                // 构建包含完整上下文的prompt
                String fullPrompt = buildFullPrompt(conversationContext, batchPrompts.get(i));

                // 调用AI服务
                String roundResponse = callAIWithManualContext(fullPrompt);

                if (roundResponse != null && !roundResponse.trim().isEmpty()) {
                    log.info("完成第 {} 轮对话，内容长度: {} 字符", i + 1, roundResponse.length());

                    // 将本轮对话结果添加到上下文中
                    conversationContext.addRound(i + 1, batchPrompts.get(i), roundResponse);
                } else {
                    log.warn("第 {} 轮对话返回空内容", i + 1);
                }
            }

            // 4. 生成最终完整文档
            String finalPrompt = buildFinalSummaryPrompt(conversationContext);
            String finalResponse = callAIWithManualContext(finalPrompt);

            log.info("完成第{}层多轮对话文档生成，最终内容长度: {} 字符",
                    context.getLevel(), finalResponse != null ? finalResponse.length() : 0);

            return finalResponse;

        } catch (Exception e) {
            log.error("多轮对话生成文档时发生错误", e);
            return null;
        }
    }

    /**
     * 构建包含完整上下文的prompt
     *
     * @param context       对话上下文
     * @param currentPrompt 当前轮次的具体任务prompt
     * @return 包含完整上下文的prompt
     */
    private String buildFullPrompt(ConversationContext context, String currentPrompt) {
        StringBuilder fullPrompt = new StringBuilder();

        // 1. 添加初始背景prompt
        fullPrompt.append(context.getInitialPrompt());

        // 2. 添加历史对话摘要
        fullPrompt.append(context.getHistorySummary());

        // 3. 添加当前轮次的具体任务
        fullPrompt.append("\n\n## 当前轮次任务：\n");
        fullPrompt.append(currentPrompt);

        return fullPrompt.toString();
    }

    /**
     * 构建最终总结prompt
     *
     * @param context 对话上下文
     * @return 最终总结prompt
     */
    private String buildFinalSummaryPrompt(ConversationContext context) {
        StringBuilder finalPrompt = new StringBuilder();

        // 添加初始背景
        finalPrompt.append(context.getInitialPrompt());

        // 添加所有轮次的分析结果
        finalPrompt.append(context.getHistorySummary());

        // 添加最终整合任务
        finalPrompt.append("\n\n## 最终任务：\n");
        finalPrompt.append("请基于以上所有轮次的分析结果，生成一份完整、连贯的文档。");
        finalPrompt.append("确保文档结构清晰，内容完整，逻辑连贯。");

        return finalPrompt.toString();
    }

    /**
     * 使用手动上下文调用AI服务（带重试机制）
     *
     * @param fullPrompt 包含完整上下文的prompt
     * @return AI响应内容
     */
    private String callAIWithManualContext(String fullPrompt) {
        Exception lastException = null;

        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                log.debug("第 {} 次尝试调用AI服务", attempt);

                // 使用simpleChatClient，不依赖记忆管理
                String result = simpleChatClient.prompt()
                        .user(fullPrompt)
                        .call()
                        .content();

                if (result != null && !result.trim().isEmpty()) {
                    log.debug("AI服务调用成功，第 {} 次尝试", attempt);
                    return result;
                }

                log.warn("AI服务返回空内容，第 {} 次尝试", attempt);

            } catch (Exception e) {
                lastException = e;
                log.warn("AI服务调用失败，第 {} 次尝试: {}", attempt, e.getMessage());

                if (attempt < MAX_RETRY_ATTEMPTS) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS * attempt); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        log.error("AI服务调用失败，已重试 {} 次", MAX_RETRY_ATTEMPTS, lastException);
        return null;
    }

    /**
     * 传统的并行分批文档生成（保持向后兼容）
     *
     * @param context 文档生成上下文
     * @return 合并后的完整文档内容
     */
    public String generateTraditionalBatchDocumentation(DocumentationGenerationContext context) {
        try {
            log.info("开始传统分批生成第{}层文档，入口点: {}", context.getLevel(), context.getEntryPointId());

            // 1. 构建分批提示词
            List<String> batchPrompts = promptBuilder.buildBatchPrompts(context);

            if (batchPrompts.isEmpty()) {
                log.error("构建分批提示词失败");
                return null;
            }

            log.info("将第{}层文档分为 {} 批处理", context.getLevel(), batchPrompts.size());

            // 2. 并行生成各批次内容
            List<CompletableFuture<String>> futures = new ArrayList<>();

            for (int i = 0; i < batchPrompts.size(); i++) {
                final int batchIndex = i;
                final String prompt = batchPrompts.get(i);

                CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        log.info("开始生成第 {} 批文档内容", batchIndex + 1);
                        String content = callAIServiceWithRetry(prompt);
                        log.info("完成第 {} 批文档生成，内容长度: {} 字符", batchIndex + 1,
                                content != null ? content.length() : 0);
                        return content;
                    } catch (Exception e) {
                        log.error("生成第 {} 批文档失败", batchIndex + 1, e);
                        return null;
                    }
                }, executorService);

                futures.add(future);
            }

            // 3. 等待所有批次完成并收集结果
            List<String> batchResults = futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());

            // 4. 合并批次结果
            String mergedContent = mergeBatchResults(batchResults, context);

            log.info("完成第{}层传统分批文档生成，最终内容长度: {} 字符",
                    context.getLevel(), mergedContent != null ? mergedContent.length() : 0);

            return mergedContent;

        } catch (Exception e) {
            log.error("传统分批生成文档时发生错误", e);
            return null;
        }
    }

    /**
     * 带重试机制的AI服务调用
     */
    private String callAIServiceWithRetry(String prompt) {
        Exception lastException = null;
        int maxRetries = 3;
        long retryDelay = 1000;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.debug("第 {} 次尝试调用AI服务", attempt);

                String response = simpleChatClient.prompt()
                        .user(prompt)
                        .call()
                        .content();

                if (response != null && !response.trim().isEmpty()) {
                    log.debug("AI服务调用成功，第 {} 次尝试", attempt);
                    return response;
                }

                log.warn("AI服务返回空内容，第 {} 次尝试", attempt);

            } catch (Exception e) {
                lastException = e;
                log.warn("AI服务调用失败，第 {} 次尝试: {}", attempt, e.getMessage());

                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(retryDelay * attempt);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        log.error("AI服务调用失败，已重试 {} 次", maxRetries, lastException);
        return null;
    }

    /**
     * 合并批次结果
     */
    private String mergeBatchResults(List<String> batchResults, DocumentationGenerationContext context) {
        if (batchResults == null || batchResults.isEmpty()) {
            return null;
        }

        // 过滤掉空结果
        List<String> validResults = batchResults.stream()
                .filter(result -> result != null && !result.trim().isEmpty())
                .toList();

        if (validResults.isEmpty()) {
            log.warn("所有批次结果都为空");
            return null;
        }

        // 简单合并策略：用分隔符连接
        StringBuilder merged = new StringBuilder();
        merged.append("# 第").append(context.getLevel()).append("层级完整说明书\n\n");

        for (int i = 0; i < validResults.size(); i++) {
            if (i > 0) {
                merged.append("\n---\n\n");
            }
            merged.append("## 第").append(i + 1).append("部分\n\n");
            merged.append(validResults.get(i));
        }

        return merged.toString();
    }
}
